name: Agric-os-v25 Form Builder App deployment workflow

on:
  push:
    branches:
      - develop

env:
  WIF_PROVIDER: ${{ secrets.DEV_WIF_PROVIDER }}
  SA_EMAIL: ${{ secrets.DEV_SA_EMAIL }}
  PROJECT_ID: ${{ secrets.DEV_GKE_PROJECT }}
  GITHUB_SHA: ${{ github.sha }}
  REGION: ${{ vars.REGION }}
  SERVICE: ${{ vars.SERVICE_NAME }}
  REPOSITORY: ${{ vars.ARTIFACT_REPO }}

jobs:
  # Job 1: GitLeak scan to check for sensitive information leaks
  gitleak-scan:
    name: GitLeaks Scan
    if: ${{ github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/develop' }}
    runs-on: ubuntu-latest
    continue-on-error: false
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run GitLeaks Scan
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE }}

      - name: Slack Notification
        if: failure()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: devops-app-notification
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_LINK_NAMES: true
          SLACK_MSG_AUTHOR: ${{ github.actor }}
          SLACK_TITLE: AgricOS-v25 Form Builder App Deployment
          SLACK_MESSAGE: "Gitleaks scan failed for Commit - ${{ github.event.head_commit.message }}"
          SLACK_USERNAME: "rtCamp"
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}

  # Job 2: Static Code Analysis with SonarQube
  static-code-analysis:
    name: Static Code Analysis with SonarQube
    runs-on: ubuntu-latest
    needs: gitleak-scan
    continue-on-error: false
    permissions:
      contents: "read"

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Cache SonarQube dependencies
        uses: actions/cache@v3
        with:
          path: ~/.sonar/cache
          key: ${{ runner.os }}-sonar-${{ hashFiles('**/pom.xml') }}

      - uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}

      - name: Check SonarQube Quality Gate
        uses: sonarsource/sonarqube-quality-gate-action@master
        timeout-minutes: 5
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

      - name: Slack Notification
        if: failure()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: devops-app-notification
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_LINK_NAMES: true
          SLACK_MSG_AUTHOR: ${{ github.actor }}
          SLACK_TITLE: AgricOS-v25 Form Builder App Deployment
          SLACK_MESSAGE: "Sonarqube scan failed for Commit - ${{ github.event.head_commit.message }}"
          SLACK_USERNAME: "rtCamp"
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}

  # Job 3: Trivy Vulnerability Scan
  build-trivy-check-and-push:
    name: Build and Trivy Vulnerability Scan
    runs-on: ubuntu-latest
    needs: [static-code-analysis]
    continue-on-error: false
    environment: development
    permissions:
      contents: "read"
      id-token: "write"
    outputs:
      short_sha: ${{ steps.truncate_sha.outputs.short_sha }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create .env file
        run: |
          cat << EOF > .env
          NEXT_PUBLIC_FORM_BUILDER_API=${{ vars.NEXT_PUBLIC_FORM_BUILDER_API }}
          NEXT_PUBLIC_CONFIG_FRONTEND_URL=${{ vars.NEXT_PUBLIC_CONFIG_FRONTEND_URL }}
          NEXT_PUBLIC_IAM_API=${{ vars.NEXT_PUBLIC_IAM_API }}
          NEXT_PUBLIC_IAM_FRONTEND_URL=${{ vars.NEXT_PUBLIC_IAM_FRONTEND_URL }}
          EOF

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ env.WIF_PROVIDER }}
          service_account: ${{ env.SA_EMAIL }}

      - name: Configure Docker to use gcloud as a credential helper
        run: |
          gcloud auth configure-docker $REGION-docker.pkg.dev --quiet

      - name: Truncate SHA
        id: truncate_sha
        run: echo "short_sha=${GITHUB_SHA:0:7}" >> $GITHUB_OUTPUT

      - name: Build Docker Image
        run: |
          docker build -t ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE }}:${{ steps.truncate_sha.outputs.short_sha }} .

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@0.28.0
        with:
          image-ref: "${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE }}:${{ steps.truncate_sha.outputs.short_sha }}"
          format: "table"
          exit-code: "1"
          ignore-unfixed: true
          vuln-type: "os,library"
          severity: "CRITICAL,HIGH"

      - name: Publish Image to Artifact Registry
        run: |
          docker push ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE }}:${{ steps.truncate_sha.outputs.short_sha }}

      - name: Slack Notification
        if: failure()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: devops-app-notification
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_LINK_NAMES: true
          SLACK_MSG_AUTHOR: ${{ github.actor }}
          SLACK_TITLE: AgricOS-v25 Form Builder App Deployment
          SLACK_MESSAGE: "Trivy scan failed for Commit - ${{ github.event.head_commit.message }}"
          SLACK_USERNAME: "rtCamp"
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}

  dev-deploy:
    name: Deploy image to Cloud run
    runs-on: ubuntu-latest
    needs: [build-trivy-check-and-push]
    if: ${{ github.ref == 'refs/heads/develop' }}
    environment: development
    permissions:
      contents: "read"
      id-token: "write"
    steps:
      - name: Authenticate to Google Cloud
        id: gcp-auth
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ env.WIF_PROVIDER }}
          service_account: ${{ env.SA_EMAIL }}

      - name: Deploy to Cloud Run
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: ${{ env.SERVICE }}
          region: ${{ env.REGION }}
          image: ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE }}:${{ needs.build-trivy-check-and-push.outputs.short_sha }}
        id: deploy

      - name: Use output
        run: curl "${{ steps.deploy.outputs.url }}"

      - name: Slack Notification
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: devops-app-notification
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_LINK_NAMES: true
          SLACK_MSG_AUTHOR: ${{ github.actor }}
          SLACK_TITLE: AgricOS-v25 Form Builder App Deployment
          SLACK_MESSAGE: "Cloud run Deployment ${{ job.status }} for Commit - ${{ github.event.head_commit.message }}"
          SLACK_USERNAME: "rtCamp"
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
