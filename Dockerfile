# Use a smaller base image
FROM node:22-alpine AS builder

# Set the working directory
WORKDIR /app

# Copy package.json and package-lock.json (if available)
COPY package*.json ./

# Install the PNPM package manager for Node.js
RUN npm install -g pnpm

# Install dependencies
RUN pnpm install

# Copy the rest of the application code
COPY . .

# Build the application
RUN pnpm build

# Use a minimal runtime base image
FROM node:22-alpine AS runtime

# Install the PNPM package manager for Node.js
RUN npm install -g pnpm

# Set the working directory
WORKDIR /app

# Copy only the necessary files from the builder stage
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/package*.json ./

# Expose the application port
EXPOSE 3000

# Define the command to run the application
CMD ["pnpm", "start"]