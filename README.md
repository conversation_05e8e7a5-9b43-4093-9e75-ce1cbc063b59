# Form Builder

The form builder is an application used to build forms for simple and advanced activities in Babban Gona

## Installation

1. **Clone the repository:**

   ```bash
   git clone https://github.com/BabbanGonaDev/agricos25-form-builder-app.git
   ```

2. **Install dependencies:**

   ```bash
   pnpm i
   ```

## Usage

Start the server for development using the following command:

```bash
pnpm dev
```

For production, build and start the server using the following commands respectively:

```bash
pnpm build
```

```bash
pnpm start
```

## Configuration

Before starting the application, make sure to set up the required environment variables in a `.env` file. You can use the `.env.example` file as a template:

```bash
cp .env.example .env
```

## External Links

<!-- - [DEV URL](https://card-verification-dev.agric-os.com)
- [PROD URL](https://card-verification.agric-os.com) -->
- [Figma Screens](https://www.figma.com/design/WToNYcLxNAZDURmZbKUeJx/Form-Builder?node-id=1717-2607&t=MF2NQtrmiFxgDxo0-1)
