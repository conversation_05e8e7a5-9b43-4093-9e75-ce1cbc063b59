{"name": "agricos25-form-builder-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write 'src/**/*.{js,jsx,ts,tsx,css,json}'", "prepare": "husky"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "@reduxjs/toolkit": "^2.5.0", "@tanstack/react-query": "^5.64.1", "axios": "^1.7.9", "change-case": "^5.4.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.1", "husky": "^9.1.7", "input-otp": "^1.4.2", "lucide-react": "^0.469.0", "moment": "^2.30.1", "next": "15.2.3", "next-themes": "^0.4.4", "nuqs": "^2.4.0", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-redux": "^9.2.0", "react-resizable-panels": "^2.1.7", "react-spinners": "^0.15.0", "react-textarea-autosize": "^8.5.7", "recharts": "^2.15.0", "redux-persist": "^6.0.0", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.1", "typescript": "^5"}, "pnpm": {"onlyBuiltDependencies": ["sharp"]}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}, "coveragePathIgnorePatterns": ["node_modules", ".spec.ts", ".module.ts", ".dto.ts", ".enum.ts", "schema.ts", ".interface.ts", "/mocks/", "/mocks/"]}, "coverageReporters": ["lcov", "text-summary"]}