{"name": "Sign Up Form", "description": "This form contains data for the sign up form to be used by the Babban Gona organization tenant.", "audio": {"name": "Name of audio file", "url": "https:www.sampleaudio.com/hgf.mp3", "format": "mp3"}, "totalScreens": 1, "screens": [{"id": "39454234", "name": "Registration Page", "description": "This is the description of the current page you are in and its components", "sections": [{"id": "sec3456523", "title": "Personal Information (Bio-Data)", "components": [{"type": "short_answer", "id": 90324523, "label": "First Name", "hint": "Enter your first name", "tooltip": "This is a sample tool tip for first-name", "minimumCharacterCount": 3, "maximumCharacterCount": 5, "required": true, "validation": true}, {"type": "short_answer", "id": 45446434, "label": "Last Name", "hint": "Enter your last name", "tooltip": "This is a sample tooltip for last-name", "minimumCharacterCount": 1, "maximumCharacterCount": 5, "required": true, "validation": false}, {"type": "paragraph", "id": 345463246, "label": "Give a brief introduction", "hint": "Enter paragraph title", "tooltip": "This is a sample tooltip for paragraph layout element", "minimumCharacterCount": 20, "maximumCharacterCount": 50, "required": false, "validation": true}, {"type": "number", "id": 957498635, "label": "Enter the number of sacks", "hint": "Number of sacks", "tooltip": "User should enter the number of sacks.", "minimumValue": 20, "maximumValue": 50, "required": false, "currency": true, "validation": false}, {"type": "phone_number", "id": 28595654, "label": "Enter your phone number", "hint": "Phone number", "tooltip": "This is a sample tool tip for phone-number", "required": true, "countryCode": {"country": "Ghana", "code": "+233"}, "validaton": {"regex": "^(?:\\+233|0)\\d{6}$"}}]}, {"id": "sec2490452332", "title": "Preferences", "components": [{"type": "date", "id": 34235653, "label": "Enter your date of birth", "hint": "Date of birth", "tooltip": "", "required": true, "minimumDate": "2024-01-01", "maximumDate": "2025-12-31", "dateFormat": "yyyy-mm-dd"}, {"type": "time", "id": 674567890, "label": "Enter your time of birth", "hint": "Time of birth", "tooltip": "", "required": true, "timeFormat": "HH:mm:ss aa", "validation": false}, {"type": "single_choice", "id": 34567890, "label": "Country", "hint": "Select your country", "tooltip": "", "required": true, "optionType": "country"}, {"type": "multi_choice", "id": 8576435, "label": "Hobbies", "hint": "Select your hobbies", "tooltip": "", "required": false, "options": ["Reading", "Watching Movies", "Sleeping"]}]}]}]}