import { FORM_BUILDER_API, IAM_API, IAM_FRONTEND_URL } from "@/config/env";
import { updateSomethingWrong, updateUserTokens } from "@/lib/redux/slices/authSlice";
import { store } from "@/lib/redux/store";
import { getUserTokens } from "@/lib/utils";
import axios from "axios";

export const formBuilderApi = axios.create({
  baseURL: FORM_BUILDER_API,
});

export const iamApi = axios.create({
  baseURL: IAM_API,
});

iamApi.interceptors.request.use(
  config => {
    const { accessToken } = store.getState().auth.userTokens;
    config.headers["Authorization"] = `Bearer ${accessToken}`;
    return config;
  },
  error => Promise.reject(error),
);

formBuilderApi.interceptors.request.use(
  config => {
    const { accessToken } = store.getState().auth.userTokens;
    config.headers["Authorization"] = `Bearer ${accessToken}`;
    return config;
  },
  error => Promise.reject(error),
);

formBuilderApi.interceptors.response.use(
  response => response,
  async error => {
    if (error.response.status === 401) {
      const { refreshToken } = getUserTokens();

      try {
        // Create a new request config from the original request
        const originalRequest = error.config;

        // Prevent infinite loops by tracking retry attempts
        if (!originalRequest._retry) {
          originalRequest._retry = true;

          // Call the refresh token endpoint
          const response = await iamApi.post("/auth/sessions/refresh", { refreshToken });

          // Check if the refresh was successful
          if (response.data && response.data.data) {
            // Update tokens in Redux store
            const newTokens = {
              accessToken: response.data.data.accessToken,
              refreshToken: response.data.data.refreshToken,
            };

            store.dispatch(updateUserTokens(newTokens));

            // Update the Authorization header with the new token
            originalRequest.headers["Authorization"] = `Bearer ${newTokens.accessToken}`;

            // Retry the original request with the new token
            return formBuilderApi(originalRequest);
          }
        }
      } catch (error: any) {
        if (error.response.status === 401) {
          const currentUrl = decodeURIComponent(window.location.href);
          window.location.href = `${IAM_FRONTEND_URL}?redirect_uri=${encodeURIComponent(currentUrl)}`;
        }
        return Promise.reject(error);
      }
    }

    if (typeof error.response.data !== "object" || error.response.status === 500) {
      store.dispatch(updateSomethingWrong(true));
    }

    return Promise.reject(error);
  },
);
