import { CreateFormServerData, ExistingFormsQueryParams, SingleFormData, UpdateFormServerData } from "@/app/(core)/forms/types";
import { getFileFormat } from "@/lib/utils";
import { formBuilderApi } from "./base";
import { AxiosProgressEvent } from "axios";

export const uploadFile = async (file: File, onUploadProgress?: (progressEvent: AxiosProgressEvent) => void) => {
  const formData = new FormData();
  formData.append("file", file);
  const config = onUploadProgress ? { onUploadProgress } : {};
  const response = await formBuilderApi.post("/upload/single", formData, config);

  return response.data.url;
};

export const createForm = async (formData: CreateFormServerData) => {
  const file = formData.audio as File;
  let fileUrl = "";
  let fileName = "";
  let fileFormat = "";
  if (file) {
    fileName = file.name;
    fileUrl = await uploadFile(file);
    fileFormat = getFileFormat(fileName);
  }
  const newFormData = {
    name: formData.name,
    description: formData.description,
    ...(file && {
      audio: {
        name: fileName,
        url: fileUrl,
        format: fileFormat,
      },
    }),
    screens: formData.screens,
  };
  const response = await formBuilderApi.post("form", newFormData);
  return response?.data.data;
};

export const getSingleForm = async (id: string) => {
  const response = await formBuilderApi(`/form/${id}`);
  return response?.data?.data;
};

export const getExistingForms = async (params: ExistingFormsQueryParams) => {
  const response = await formBuilderApi(`/form/submitted`, { params });
  return response?.data?.data;
};

export const updateForm = async (id: string, formData: UpdateFormServerData): Promise<SingleFormData> => {
  return await formBuilderApi.patch(`form/${id}`, formData);
};

export const submitForm = async (id: string) => {
  await formBuilderApi.patch(`form/submit/${id}`);
};
