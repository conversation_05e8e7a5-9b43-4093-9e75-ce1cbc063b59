"use client";
import { SingleFormData } from "@/app/(core)/forms/types";
import FormBuilder from "@/components/FormBuilder";
import FormName from "@/components/FormName";
import FormNotFound from "@/components/FormNotFound";
import PageWrapper from "@/components/PageWrapper";
import { useGetSingleForm } from "@/hooks/tansack-query/queries/use-forms";
import { useAppDispatch } from "@/hooks/use-redux";
import { updateFormId } from "@/lib/redux/slices/formSlice";
import { use, useEffect } from "react";
import ClipLoader from "react-spinners/ClipLoader";

const SingleForm = ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = use(params);
  const { singleFormLoading, singleFormData, singleFormError } = useGetSingleForm(id);
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(updateFormId(id));
  }, [dispatch, id]);

  return (
    <>
      {singleFormLoading ? (
        <div className="mt-10 flex h-[80vh] items-center justify-center">
          <ClipLoader color="#58CC02" size={60} />
        </div>
      ) : singleFormError?.response?.status === 404 ? (
        <PageWrapper>
          <FormNotFound />
        </PageWrapper>
      ) : (
        <PageWrapper
          pageTitle={<FormName name={singleFormData?.name || ""} description={singleFormData?.description || ""} />}
          organization={singleFormData?.organization}
        >
          <FormBuilder form={singleFormData as SingleFormData} />
        </PageWrapper>
      )}
    </>
  );
};

export default SingleForm;
