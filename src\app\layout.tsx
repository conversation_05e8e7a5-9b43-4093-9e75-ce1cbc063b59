import SuccessDialog from "@/components/dialogs/SuccessDialog";
import { Toaster } from "@/components/ui/toaster";
import Providers from "@/providers";
import type { Metadata } from "next";
import { Suspense } from "react";
import { bloggerSans } from "./fonts";
import "./globals.css";

export const metadata: Metadata = {
  title: "Form Builder",
  description: "The form builder is an application used to build forms for simple and advanced activities in Babban Gona",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${bloggerSans.className} bg-[#F2F2F4] text-[#252244]`}>
        <Providers>
          <Suspense>{children}</Suspense>
          <SuccessDialog />
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}
