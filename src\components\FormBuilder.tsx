import { SingleFormData } from "@/app/(core)/forms/types";
import { fredoka } from "@/app/fonts";
import CreateFormDialog from "@/components/dialogs/CreateFormDialog";
import DragOverlayWrapper from "@/components/DragOverlayWrapper";
import ScreenLayout from "@/components/layouts/Screen/ScreenLayout";
import MenuBar from "@/components/MenuBar";
import PropertiesBar from "@/components/PropertiesBar";
import StatusIndicator from "@/components/StatusIndicator";
import { Button } from "@/components/ui/button";
import { useSubmitForm, useUpdateForm } from "@/hooks/tansack-query/mutations/use-forms";
import useFormDndMonitor from "@/hooks/use-form-dnd-monitor";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { closeCreateFormDialog, openCreateFormDialog } from "@/lib/redux/slices/dialogSlice";
import { replaceFormScreens, updateSelectedFormBuilderItem } from "@/lib/redux/slices/formSlice";
import { createNewScreen, formatScreensForUI } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import ClipLoader from "react-spinners/ClipLoader";

const FormBuilder = ({ form }: { form: SingleFormData }) => {
  const dispatch = useAppDispatch();
  const formId = useAppSelector(state => state.form.formId);
  const router = useRouter();
  const formScreens = useAppSelector(state => state.form.formScreens);
  const { submitSingleForm, isSubmittingSingleForm } = useSubmitForm();
  const { updateSingleForm, isUpdatingSingleForm, isUpdateSingleFormSuccess, updateSingleFormData } = useUpdateForm();
  const formattedScreens = formatScreensForUI(form.screens);

  const handleCreateBlankForm = () => {
    updateSingleForm({
      screens: [createNewScreen()],
    });
  };

  const handleOpenExistingForm = async () => {
    dispatch(closeCreateFormDialog());
    router.push("/forms/existing");
  };

  const handleBack = () => {
    window.location.replace(sessionStorage.getItem("returnUrl") || "");
  };

  useEffect(() => {
    dispatch(replaceFormScreens(formattedScreens));
    dispatch(updateSelectedFormBuilderItem({ id: form.screens[0]?.id, type: "ScreenLayout" }));
    form.totalScreens === 0 && dispatch(openCreateFormDialog());
  }, []);

  useEffect(() => {
    if (isUpdateSingleFormSuccess) {
      router.push(`/forms/${formId}?returnUrl=${sessionStorage.getItem("returnUrl") || ""}`);
      const formattedScreens = formatScreensForUI(updateSingleFormData.data.data?.screens);
      dispatch(replaceFormScreens(formattedScreens));
    }
  }, [isUpdateSingleFormSuccess]);

  useFormDndMonitor(formScreens);

  return (
    <>
      <div className="flex gap-4">
        <MenuBar />
        <div className="mx-[280px] mt-20 w-full px-4">
          <div className="space-y-1">
            <div className="mt-2">
              <StatusIndicator />
            </div>
            <div className="space-y-4">
              {formScreens.map(screen => (
                <div key={screen.id}>
                  <ScreenLayout screen={screen} />
                </div>
              ))}
            </div>
          </div>
          <div className={`mt-16 flex items-center justify-between ${form.totalScreens === 0 && "hidden"}`}>
            <Button className={`h-[3.4rem] w-40 ${fredoka.className} text-lg font-semibold`} variant="yellow" onClick={handleBack}>
              Back
            </Button>
            <div className="flex-end flex items-center gap-4">
              {form.responseReceived || (
                <Button
                  className={`h-[3.4rem] w-40 disabled:border-primary-green disabled:bg-primary-green ${fredoka.className} text-lg font-semibold`}
                  disabled={isSubmittingSingleForm}
                  onClick={() => submitSingleForm()}
                >
                  {isSubmittingSingleForm ? <ClipLoader color="#fff" size={30} /> : "Submit"}
                </Button>
              )}
            </div>
          </div>
        </div>
        <PropertiesBar />
        <CreateFormDialog
          isCreatingBlankForm={isUpdatingSingleForm}
          handleOpenExistingForm={handleOpenExistingForm}
          handleCreateBlankForm={handleCreateBlankForm}
        />
        <DragOverlayWrapper />
      </div>
    </>
  );
};

export default FormBuilder;
