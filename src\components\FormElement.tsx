import { FormElementProps } from "@/components/types";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { updateSelectedFormBuilderItem, updateSelectedFormBuilderItemScreen } from "@/lib/redux/slices/formSlice";
import { cn } from "@/lib/utils";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

const FormElement = ({ element, Element, screenId, sectionId }: FormElementProps) => {
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem);

  const { setNodeRef, attributes, listeners, transform, transition, isDragging, isOver } = useSortable({
    id: element.id,
    data: {
      isSection: false,
      isFormElement: true,
      screenId,
      sectionId,
      element,
    },
  });

  const style = {
    transition,
    transform: CSS.Transform.toString(transform),
  };

  const handleElementClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    dispatch(updateSelectedFormBuilderItem(element));
    dispatch(updateSelectedFormBuilderItemScreen(screenId));
  };

  return (
    <>
      {isDragging ? (
        <div ref={setNodeRef} style={style} className="relative h-[7.5rem] rounded-[10px] border-2 border-primary-green opacity-60"></div>
      ) : (
        <div
          ref={setNodeRef}
          {...attributes}
          {...listeners}
          style={style}
          className={cn(
            "relative rounded-[10px] border border-primary-gray",
            `${selectedFormBuilderItem?.id === element.id && "border-primary-green"}`,
            isOver && "border-primary-green",
          )}
          onClick={handleElementClick}
        >
          <div>
            <Element element={element} screenId={screenId} sectionId={sectionId} />
          </div>
        </div>
      )}
    </>
  );
};

export default FormElement;
