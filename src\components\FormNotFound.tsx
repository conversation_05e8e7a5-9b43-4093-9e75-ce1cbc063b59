import { fredoka } from "@/app/fonts";
import FormNotFoundIcon from "@/assets/icons/form-not-found.svg";
import BetterLifeLogo from "@/assets/images/better-life-logo.svg";
import DummyCompanyLogo from "@/assets/images/dummy-company-logo.svg";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Button } from "./ui/button";

const FormNotFound = () => {
  const router = useRouter();
  return (
    <>
      <div className="flex h-screen flex-col gap-0">
        <div className="fixed left-0 top-0 z-10 flex h-20 w-full items-center justify-between bg-white p-4">
          <Image src={DummyCompanyLogo} alt="Better Life Image" className="h-10 w-10" />
          <Image src={BetterLifeLogo} alt="Better Life Image" className="h-10 w-10" />
        </div>
        <div className="mt-20 flex flex-grow flex-col items-center justify-center pt-4">
          <div className="flex h-[262px] w-[400px] flex-col gap-[30px]">
            <div className="flex flex-col items-center gap-6">
              <Image src={FormNotFoundIcon} alt={"Form Not Found"}></Image>
              <p className={`${fredoka.className} text-lg font-semibold`}>Form Not Found</p>
            </div>
            <Button
              className={`h-[54px] w-auto rounded-[10px] px-[10px] py-[17px] ${fredoka.className} text-lg font-semibold`}
              onClick={() => router.push("/forms")}
            >
              Back to homepage
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default FormNotFound;
