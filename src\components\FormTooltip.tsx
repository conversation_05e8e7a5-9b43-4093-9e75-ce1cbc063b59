import { inter } from "@/app/fonts";
import InfoIcon from "@/assets/icons/info.svg";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import Image from "next/image";
import { FormTooltipProps } from "./types";
const FormTooltip = ({ description, side = "right" }: FormTooltipProps) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <Image src={InfoIcon} alt="Info Icon" />
        </TooltipTrigger>

        <TooltipContent
          side={side}
          className={`${inter.className} relative max-w-[21.25rem] text-base tracking-[-1.1%] text-[#252244] shadow-[0_10px_30px_3px_rgba(5,16,55,0.15)]`}
        >
          <p>{description}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default FormTooltip;
