import FormTooltip from "@/components/FormTooltip";
import { LabelInputProps } from "@/components/types";
import { AutosizeTextarea } from "@/components/ui/autosize-textarea";
import { useState } from "react";

const LabelInput = ({ label, required, tooltip, onLabelChange }: LabelInputProps) => {
  const [editMode, setEditMode] = useState<boolean>(false);
  return (
    <div className="flex items-start gap-2" onClick={() => setEditMode(true)}>
      {editMode ? (
        <AutosizeTextarea
          className="border-none bg-transparent p-0"
          placeholder="Question"
          value={label}
          onChange={onLabelChange}
          autoFocus
          onBlur={() => setEditMode(false)}
          onKeyDown={e => {
            if (e.key !== "Escape") return;
            setEditMode(false);
          }}
        ></AutosizeTextarea>
      ) : (
        <p className={`w-fit whitespace-pre-wrap ${label || "text-muted-foreground"}`}>{label || "Question"}</p>
      )}
      {required || <p className="text-primary-gray">(Optional)</p>}
      {tooltip && <FormTooltip description={tooltip} side="top" />}
    </div>
  );
};

export default LabelInput;
