import { MenuBarElementProps } from "@/components/types";
import { COMPONENT_PROPERTIES_MAPPING } from "@/lib/constants";
import { getElementType } from "@/lib/utils";
import { useDraggable } from "@dnd-kit/core";
import Image from "next/image";

const MenuBarElement = ({ icon, name, isDragged = false }: MenuBarElementProps) => {
  const elementType = getElementType(name);
  const draggable = useDraggable({
    id: `menu-element-${name.toLowerCase()}`,
    data: {
      elementName: name,
      elementIcon: icon,
      element: COMPONENT_PROPERTIES_MAPPING[elementType],
      isMenuBarElement: true,
      isSection: name === "Section" ? true : false,
    },
  });

  if (isDragged) {
    return (
      <div className={"mt-2 flex h-[3.25rem] cursor-grab items-center gap-4 rounded-[10px] border border-[#252244] px-2"}>
        <Image src={icon} alt={`${name} icon`} width={36} height={36} /> {name}
      </div>
    );
  }
  return (
    <div
      ref={draggable.setNodeRef}
      {...draggable.listeners}
      {...draggable.attributes}
      className={`mt-2 flex h-[3.25rem] cursor-grab items-center gap-4 rounded-[10px] border border-[#252244] px-2 ${draggable.isDragging && "border-primary-orange bg-secondary-yellow text-primary-orange ring-1 ring-primary-orange"}`}
    >
      <Image src={icon} alt={`${name} icon`} width={36} height={36} /> {name}
    </div>
  );
};

export default MenuBarElement;
