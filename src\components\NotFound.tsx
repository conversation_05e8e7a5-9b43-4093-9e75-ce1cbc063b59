"use client";
import { fredoka } from "@/app/fonts";
import UnpluggedIcon from "@/assets/icons/unplugged.svg";
import { CONFIG_FRONTEND_URL } from "@/config/env";
import Image from "next/image";
import { Button } from "./ui/button";

const NotFound = () => {
  const redirectToConfigApp = () => {
    window.location.href = CONFIG_FRONTEND_URL as string;
  };

  return (
    <div className="flex h-screen flex-col items-center justify-center">
      <Image src={UnpluggedIcon} alt="Unplugged Icon" width={500} height={500} className="mb-4 h-[60px] w-[60px]" />
      <div className="flex flex-col gap-3 text-center">
        <p className={`text-[2.5rem] font-semibold ${fredoka.className}`}>404</p>
        <p className={`text-[1.25rem] font-semibold ${fredoka.className} opacity-60`}>Page not found</p>
        <p className="text-[1.25rem]">Sorry we cannot find the page you are looking for</p>
        <Button className={`${fredoka.className}text-lg mt-4 h-[54px] w-full font-semibold`} onClick={redirectToConfigApp}>
          Back to homepage
        </Button>
      </div>
    </div>
  );
};

export default NotFound;
