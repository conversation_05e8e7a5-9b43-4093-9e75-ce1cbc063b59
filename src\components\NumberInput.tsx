"use client";
import { NumberInputProps } from "@/components/types";
import { Input } from "@/components/ui/input";
import type { ChangeEvent } from "react";

const NumberInput = ({
  id = "number-input",
  placeholder = "Enter a number",
  value: propValue = "",
  onChange,
  min,
  max,
  step = 1,
  disabled = false,
  className = "",
  ...props // Spread the rest of the props
}: NumberInputProps) => {
  // Convert the prop value to string for display
  const displayValue = propValue?.toString() || "";

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // Only allow digits and a single decimal point
    if (newValue === "" || /^[0-9]*\.?[0-9]*$/.test(newValue)) {
      if (onChange) {
        if (newValue === "" || newValue === ".") {
          onChange(newValue);
        } else {
          const numValue = Number.parseFloat(newValue);

          // Apply min/max constraints if provided
          if (min !== undefined && numValue < Number(min)) {
            onChange(min);
            return;
          }

          if (max !== undefined && numValue > Number(max)) {
            onChange(max);
            return;
          }

          onChange(numValue);
        }
      }
    }
  };

  return (
    <Input
      id={id}
      type="text"
      inputMode="decimal"
      value={displayValue}
      onChange={handleChange}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      {...props} // Pass through all other props
    />
  );
};

export default NumberInput;
