"use client";
import { fredoka } from "@/app/fonts";
import refreshIcon from "@/assets/icons/refresh.svg";
import { Button } from "@/components/ui/button";
import { useAppDispatch } from "@/hooks/use-redux";
import { updateSomethingWrong } from "@/lib/redux/slices/authSlice";
import Image from "next/image";
import { useRouter } from "next/navigation";

const SomethingWentWrong = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();

  const handleSomethingWentWrong = () => {
    dispatch(updateSomethingWrong(false));
    router.refresh();
  };

  return (
    <div className="grid place-items-center">
      <div className="grid h-screen w-[33rem] place-content-center place-items-center">
        <Image src={refreshIcon} alt="Refresh Icon" width={500} height={500} className="h-[40px] w-[40px]" />
        <div className="flex flex-col gap-3 text-center">
          <p className={`font-700 mt-8 text-[1.875rem]/[45px] ${fredoka.className}`}>Something went wrong</p>
          <p className={`text-[1.25rem] opacity-70 ${fredoka.className}`}>Refresh page</p>
        </div>
        <Button className={`${fredoka.className} font-700 mt-6 h-[54px] w-full`} onClick={handleSomethingWentWrong}>
          Try again
        </Button>
      </div>
    </div>
  );
};

export default SomethingWentWrong;
