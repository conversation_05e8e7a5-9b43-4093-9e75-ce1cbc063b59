import { fredoka } from "@/app/fonts";
import AudioRecordingIcon from "@/assets/icons/audio-record-icon.svg";
import Hint from "@/components/Hint";
import LabelInput from "@/components/LabelInput";
import { BaseFieldProps } from "@/components/types";
import { updateFormElementOnInputChange } from "@/lib/utils";
import Image from "next/image";

function AudioRecordingField({ element, screenId, sectionId }: BaseFieldProps) {
  const { label, hint, tooltip, required } = element;
  const handleLabelChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateFormElementOnInputChange({ label: e.target.value }, screenId, sectionId);
  };

  return (
    <div className="relative">
      <div className="space-y-4 px-6 pb-8 pt-6">
        <LabelInput label={label} required={required} tooltip={tooltip} onLabelChange={handleLabelChange} />
        <div className={`mt-[8px] h-[343px] rounded-[20px] border border-dashed border-primary-green`}>
          <div className="flex h-full flex-col items-center justify-center gap-3 px-5 py-5">
            <Image src={AudioRecordingIcon} alt="upload icon" className="h-10 w-10" />
            <p className={`${fredoka.className} text-lg font-semibold`}>Record Audio</p>
            <p className="text-primary-gray">Please make sure your environment is quiet</p>
            <div
              className={`${fredoka.className} flex h-[2.5rem] w-[10rem] items-center justify-center rounded-[10px] border border-primary-gray px-4 text-lg font-semibold text-primary-gray`}
            >
              Record Audio
            </div>
          </div>
        </div>
        {hint && <Hint text={hint} />}
      </div>
    </div>
  );
}

export default AudioRecordingField;
