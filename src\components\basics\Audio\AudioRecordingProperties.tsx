import { AudioRecordingPropertiesFormData } from "@/app/(core)/forms/types";
import { FormElement } from "@/components/types";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { findFormElementSectionId } from "@/lib/utils";
import { audioRecordingPropertiesSchema } from "@/schemas/properties/audioRecordingProperties";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

const AudioRecordingProperties = () => {
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const elementId = selectedFormBuilderItem?.id ?? "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) ?? "";
  }

  const form = useForm<AudioRecordingPropertiesFormData>({
    resolver: zodResolver(audioRecordingPropertiesSchema),
    mode: "onChange",
    defaultValues: {
      minimumDuration: selectedFormBuilderItem?.minimumDuration,
      maximumDuration: selectedFormBuilderItem?.maximumDuration,
    },
    shouldFocusError: false,
  });

  const {
    setValue,
    formState: { errors },
  } = form;

  useEffect(() => {
    // Update the form values when selectedFormBuilderItem changes
    setValue("minimumDuration", selectedFormBuilderItem?.minimumDuration || "00:00:02");
    setValue("maximumDuration", selectedFormBuilderItem?.maximumDuration || "00:00:30");
  }, [selectedFormBuilderItem]);

  const applyChanges = (data: AudioRecordingPropertiesFormData) => {
    const newFormElement = {
      ...selectedFormBuilderItem,
      ...data,
    } as FormElement;
    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  return (
    <Form {...form}>
      <form onChange={form.handleSubmit(applyChanges)}>
        <div className="flex flex-col gap-2">
          <FormField
            control={form.control}
            name="minimumDuration"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Minimum Duration</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="HH:mm:ss"
                    onChange={field.onChange}
                    className={`${errors.minimumDuration && "border-destructive"}`}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="maximumDuration"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Maximum Duration</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="HH:mm:ss" // Update the placeholder
                    onChange={field.onChange}
                    className={`${errors.maximumDuration && "border-destructive"}`}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  );
};

export default AudioRecordingProperties;
