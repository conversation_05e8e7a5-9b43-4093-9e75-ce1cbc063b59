import Hint from "@/components/Hint";
import LabelInput from "@/components/LabelInput";
import { BaseFieldProps } from "@/components/types";
import { AutosizeTextarea } from "@/components/ui/autosize-textarea";
import { Input } from "@/components/ui/input";
import { useAppDispatch } from "@/hooks/use-redux";
import { updateSelectedFormBuilderItem } from "@/lib/redux/slices/formSlice";
import { updateFormElementOnInputChange } from "@/lib/utils";
import { useEffect } from "react";

const MultipleChoiceField = ({ element, screenId, sectionId }: BaseFieldProps) => {
  const dispatch = useAppDispatch();
  useEffect(() => {
    if (element.type === "MultipleChoiceField") {
      dispatch(updateSelectedFormBuilderItem(element));
    }
  }, [element]);

  const { label, hint, options = [], addOther, addCheckAll, tooltip, required, validate } = element;

  const handleLabelChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateFormElementOnInputChange({ label: e.target.value }, screenId, sectionId);
  };

  const handleOptionsChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const newOptions = [...options];
    newOptions && (newOptions[index] = e.target.value);
    updateFormElementOnInputChange({ options: newOptions }, screenId, sectionId);
    updateSelectedFormBuilderItem(element);
  };

  return (
    <div className="relative space-y-6 px-6 pb-8 pt-6">
      <div>
        <div className="space-y-4">
          <LabelInput label={label} required={required} tooltip={tooltip} onLabelChange={handleLabelChange} />
          <div className="space-y-2 rounded-[5px] border border-primary-gray px-4 pb-4 pt-4">
            {addCheckAll && (
              <div className="flex items-center gap-2">
                <div className="h-[14px] w-[14px] border-2 border-solid border-primary-gray" />
                <p className="text-sm">Check All</p>
              </div>
            )}
            {options.map((option, i) => (
              <div key={i}>
                <div className="flex items-center space-x-2">
                  <div className="h-[14px] w-[14px] border-2 border-solid border-primary-gray" />
                  <Input
                    className="h-6 border-none bg-transparent p-0 text-sm"
                    placeholder={`Option ${i + 1}`}
                    value={option}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleOptionsChange(e, i)}
                  />
                </div>
              </div>
            ))}
            {addOther && (
              <div className="flex items-center gap-2">
                <div className="h-[14px] w-[14px] border-2 border-solid border-primary-gray" />
                <p className="text-sm">Add "Other"</p>
              </div>
            )}
          </div>
          {hint && <Hint text={hint} />}
        </div>
      </div>
      <div className={`space-y-4 ${validate ? "block" : "hidden"}`}>
        <div className="flex items-start gap-2">
          <AutosizeTextarea
            className="border-none bg-transparent p-0"
            placeholder="Question"
            value={`Confirm: ${label || "Question"}`}
            readOnly
            onChange={handleLabelChange}
          ></AutosizeTextarea>
          {/* {required || <p className="text-primary-gray">(Optional)</p>} */}
          {/* {tooltip && <FormTooltip description={tooltip} side="top" />} */}
        </div>
        <div className="space-y-2 rounded-[5px] border border-primary-gray px-4 pb-4 pt-4">
          {addCheckAll && (
            <div className="flex items-center gap-2">
              <div className="h-[14px] w-[14px] border-2 border-solid border-primary-gray" />
              <p className="text-sm">Check All</p>
            </div>
          )}
          {options.map((option, i) => (
            <div key={i}>
              <div className="flex items-center space-x-2">
                <div className="h-[14px] w-[14px] border-2 border-solid border-primary-gray" />
                <Input
                  className="h-6 border-none bg-transparent p-0 text-sm"
                  placeholder={`Option ${i + 1}`}
                  value={option}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleOptionsChange(e, i)}
                  readOnly
                />
              </div>
            </div>
          ))}
          {addOther && (
            <div className="flex items-center gap-2">
              <div className="h-[14px] w-[14px] border-2 border-solid border-primary-gray" />
              <p className="text-sm">Add "Other"</p>
            </div>
          )}
        </div>
        {hint && <Hint text={hint} />}
      </div>
    </div>
  );
};

export default MultipleChoiceField;
