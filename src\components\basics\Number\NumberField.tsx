import { fredoka } from "@/app/fonts";
import Hint from "@/components/Hint";
import LabelInput from "@/components/LabelInput";
import NumberInput from "@/components/NumberInput";
import { BaseFieldProps } from "@/components/types";
import { AutosizeTextarea } from "@/components/ui/autosize-textarea";
import { Input } from "@/components/ui/input";
import { useGetSingleForm } from "@/hooks/tansack-query/queries/use-forms";
import { useAppSelector } from "@/hooks/use-redux";
import { updateFormElementOnInputChange } from "@/lib/utils";

const NumberField = ({ element, screenId, sectionId }: BaseFieldProps) => {
  const { label, placeholder, hint, currency, tooltip, required, validate } = element;
  const formId = useAppSelector(state => state.form.formId);
  const { singleFormData } = useGetSingleForm(formId);

  const handleLabelChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateFormElementOnInputChange({ label: e.target.value }, screenId, sectionId);
  };

  const handlePlaceholderChange = (value: string | number) => {
    updateFormElementOnInputChange({ placeholder: String(value) }, screenId, sectionId);
  };

  return (
    <div className="relative space-y-6 px-6 pb-8 pt-6">
      <div>
        <div className="space-y-4">
          <LabelInput label={label} required={required} tooltip={tooltip} onLabelChange={handleLabelChange} />
          <div className="relative">
            <NumberInput
              placeholder="Enter Default Value"
              value={placeholder}
              onChange={value => handlePlaceholderChange(value)}
              className={`${currency && "pl-6"}`}
            />
            {currency && (
              <p className={`${fredoka.className} absolute left-2 top-1/2 -translate-y-1/2 text-lg text-primary-gray`}>
                {singleFormData?.country.unicode_symbol}
              </p>
            )}
          </div>
        </div>
        {hint && <Hint text={hint} />}
      </div>
      <div className={`space-y-4 ${validate ? "block" : "hidden"}`}>
        <div className="flex items-start gap-2">
          <AutosizeTextarea
            className="border-none bg-transparent p-0"
            placeholder="Question"
            value={`Confirm: ${label ?? "Question"}`}
            readOnly
            onChange={handleLabelChange}
          ></AutosizeTextarea>
        </div>
        <div className="relative">
          <Input
            placeholder="Enter Default Value"
            value={placeholder}
            type="number"
            onChange={e => handlePlaceholderChange(e.target.value)}
            className={`${currency && "pl-6"}`}
            readOnly
          />
          {currency && (
            <p className={`${fredoka.className} absolute left-2 top-1/2 -translate-y-1/2 text-lg text-primary-gray`}>
              {singleFormData?.country.unicode_symbol}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default NumberField;
