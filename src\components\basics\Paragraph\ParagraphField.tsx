import Hint from "@/components/Hint";
import LabelInput from "@/components/LabelInput";
import { BaseFieldProps } from "@/components/types";
import { AutosizeTextarea } from "@/components/ui/autosize-textarea";
import { Textarea } from "@/components/ui/textarea";
import { updateFormElementOnInputChange } from "@/lib/utils";

const ParagraphField = ({ element, screenId, sectionId }: BaseFieldProps) => {
  const { label, placeholder, hint, tooltip, required, validate } = element;

  const handleLabelChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateFormElementOnInputChange({ label: e.target.value }, screenId, sectionId);
  };

  const handlePlaceholderChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateFormElementOnInputChange({ placeholder: e.target.value }, screenId, sectionId);
  };

  return (
    <div className="relative space-y-6 px-6 pb-8 pt-6">
      <div>
        <div className="space-y-4">
          <LabelInput label={label} required={required} tooltip={tooltip} onLabelChange={handleLabelChange} />
          <Textarea className="h-[8rem] resize-none" placeholder="Default Value" value={placeholder} onChange={handlePlaceholderChange}></Textarea>
        </div>
        {hint && <Hint text={hint} />}
      </div>
      <div className={`space-y-4 ${validate ? "block" : "hidden"}`}>
        <AutosizeTextarea className="border-none bg-transparent p-0" value={`Confirm: ${label || "Question"}`} readOnly></AutosizeTextarea>
        <Textarea
          className="h-[8rem] resize-none"
          placeholder="Enter Default Value"
          value={placeholder}
          onChange={handlePlaceholderChange}
          readOnly
        />
      </div>
    </div>
  );
};

export default ParagraphField;
