import FormTooltip from "@/components/FormTooltip";
import Hint from "@/components/Hint";
import LabelInput from "@/components/LabelInput";
import { BaseFieldProps } from "@/components/types";
import { AutosizeTextarea } from "@/components/ui/autosize-textarea";
import { Input } from "@/components/ui/input";
import { updateFormElementOnInputChange } from "@/lib/utils";

const PhoneNumberField = ({ element, screenId, sectionId }: BaseFieldProps) => {
  const { label, hint, tooltip, required, validate } = element;
  const handleLabelChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateFormElementOnInputChange({ label: e.target.value }, screenId, sectionId);
  };
  return (
    <div className="relative space-y-6 px-6 pb-8 pt-6">
      <div>
        <div className="space-y-4">
          <LabelInput label={label} required={required} tooltip={tooltip} onLabelChange={handleLabelChange} />
          <Input placeholder="+234 0000 000 0000" />
          {hint && <Hint text={hint} />}
        </div>
      </div>
      <div className={`space-y-4 ${validate ? "block" : "hidden"}`}>
        <div className="flex items-start gap-2">
          <AutosizeTextarea
            className="h-6 w-full border-none bg-transparent p-0"
            placeholder="Question"
            value={`Confirm: ${label || "Question"}`}
            readOnly
            onChange={handleLabelChange}
          />
          {required || <p className="text-primary-gray">(Optional)</p>}
          {tooltip && <FormTooltip description={tooltip} side="top" />}
        </div>
        <Input placeholder="+234 0000 000 0000" readOnly />
        {hint && <Hint text={hint} />}
      </div>
    </div>
  );
};

export default PhoneNumberField;
