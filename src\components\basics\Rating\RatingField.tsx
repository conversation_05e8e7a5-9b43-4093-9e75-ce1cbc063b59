import FormTooltip from "@/components/FormTooltip";
import Hint from "@/components/Hint";
import LabelInput from "@/components/LabelInput";
import { BaseFieldProps } from "@/components/types";
import { AutosizeTextarea } from "@/components/ui/autosize-textarea";
import { Input } from "@/components/ui/input";
import { updateFormElementOnInputChange } from "@/lib/utils";

const RatingField = ({ element, screenId, sectionId }: BaseFieldProps) => {
  const { label, hint, tooltip, lowestRating, highestRating, required, validate } = element;
  const handleLabelChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateFormElementOnInputChange({ label: e.target.value }, screenId, sectionId);
  };

  const handleLowestRatingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateFormElementOnInputChange({ lowestRating: e.target.value }, screenId, sectionId);
  };

  const handleHighestRatingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateFormElementOnInputChange({ highestRating: e.target.value }, screenId, sectionId);
  };

  const numbersOfRating = element.numbersOfRating || 10; // Default to 10

  return (
    <div className="relative space-y-6 px-6 pb-8 pt-6">
      <div>
        <div className="space-y-4">
          <LabelInput label={label} required={required} tooltip={tooltip} onLabelChange={handleLabelChange} />
          <div className="flex justify-between">
            {Array.from({ length: numbersOfRating }, (_, i) => i + 1).map(rating => (
              <span key={rating} className="flex h-[36px] w-[36px] items-center justify-center rounded-[18px] bg-[#F8DF3F]">
                {rating}
              </span>
            ))}
          </div>
          <div className="flex justify-between">
            <Input
              className="h-6 w-32 border-none bg-transparent p-0 text-left text-primary-gray"
              placeholder="Very Poor"
              value={lowestRating}
              onChange={handleLowestRatingChange}
            />
            <Input
              className="h-6 w-32 border-none bg-transparent p-0 text-right text-primary-gray"
              placeholder="Very Good"
              value={highestRating}
              onChange={handleHighestRatingChange}
            />
          </div>
          {hint && <Hint text={hint} />}
        </div>
      </div>
      <div className={`space-y-4 ${validate ? "block" : "hidden"}`}>
        <div className="flex items-start gap-2">
          <AutosizeTextarea
            className="h-6 border-none bg-transparent p-0"
            placeholder="Question"
            value={`Confirm: ${label || "Question"}`}
            readOnly
            onChange={handleLabelChange}
          />
          {required || <p className="text-primary-gray">(Optional)</p>}
          {tooltip && <FormTooltip description={tooltip} side="top" />}
        </div>
        <div className="pointer-events-none flex justify-between">
          {Array.from({ length: numbersOfRating }, (_, i) => i + 1).map(rating => (
            <span key={rating} className="flex h-[36px] w-[36px] items-center justify-center rounded-[18px] bg-[#F8DF3F]">
              {rating}
            </span>
          ))}
        </div>
        <div className="flex justify-between">
          <Input
            className="h-6 w-32 border-none bg-transparent p-0 text-left text-primary-gray"
            placeholder="Very Poor"
            value={lowestRating}
            onChange={handleLowestRatingChange}
            readOnly
          />
          <Input
            className="h-6 w-32 border-none bg-transparent p-0 text-right text-primary-gray"
            placeholder="Very Good"
            value={highestRating}
            onChange={handleHighestRatingChange}
            readOnly
          />
        </div>
        {hint && <Hint text={hint} />}
      </div>
    </div>
  );
};

export default RatingField;
