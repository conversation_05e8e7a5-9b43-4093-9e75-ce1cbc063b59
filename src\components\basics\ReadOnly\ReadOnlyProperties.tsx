import { ReadOnlyPropertiesFormData } from "@/app/(core)/forms/types";
import { FormElement } from "@/components/types";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { READ_ONLY_CATEGORY } from "@/lib/constants";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { findFormElementSectionId } from "@/lib/utils";
import { readOnlyPropertiesSchema } from "@/schemas/properties/readOnlyProperties";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

const ReadOnlyProperties = () => {
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const elementId = selectedFormBuilderItem?.id ?? "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) ?? "";
  }

  const form = useForm<ReadOnlyPropertiesFormData>({
    resolver: zodResolver(readOnlyPropertiesSchema),
    mode: "onChange",
    defaultValues: {
      category: selectedFormBuilderItem?.category || "Title Only",
    },
    shouldFocusError: false,
  });

  const {
    getValues,
    setValue,
    formState: { errors },
  } = form;

  useEffect(() => {
    setValue("category", selectedFormBuilderItem?.category || "Title Only");
  }, [selectedFormBuilderItem]);

  const updateFormElements = (data: ReadOnlyPropertiesFormData) => {
    const currentData = { ...getValues() };
    let newFormElement = {
      ...selectedFormBuilderItem,
      ...currentData,
    } as FormElement;

    if (currentData.category === "Title Only") {
      const { fileUrl, ...modifiedSelectedBuilderItem } = selectedFormBuilderItem;
      newFormElement = {
        ...modifiedSelectedBuilderItem,
        ...currentData,
      } as FormElement;
    }

    if (currentData.category === "File Only") {
      const { title, ...modifiedSelectedBuilderItem } = selectedFormBuilderItem;
      newFormElement = {
        ...modifiedSelectedBuilderItem,
        ...currentData,
      } as FormElement;
    }

    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  return (
    <Form {...form}>
      <form onChange={form.handleSubmit(updateFormElements)}>
        <div className="flex flex-col gap-2">
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <SelectTrigger className="data-[placeholder]:text-gray-500">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {READ_ONLY_CATEGORY.map(item => (
                        <SelectItem key={item} value={item}>
                          {item}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  );
};

export default ReadOnlyProperties;
