import Hint from "@/components/Hint";
import LabelInput from "@/components/LabelInput";
import { BaseFieldProps } from "@/components/types";
import { Input } from "@/components/ui/input";
import { updateFormElementOnInputChange } from "@/lib/utils";

const ShortAnswerField = ({ element, screenId, sectionId }: BaseFieldProps) => {
  const { label, placeholder, hint, tooltip, required, validate } = element;

  const handleLabelChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateFormElementOnInputChange({ label: e.target.value }, screenId, sectionId);
  };

  const handlePlaceholderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateFormElementOnInputChange({ placeholder: e.target.value }, screenId, sectionId);
  };

  return (
    <div className="relative space-y-6 px-6 pb-8 pt-6">
      <div>
        <div className="space-y-4">
          <LabelInput label={label} required={required} tooltip={tooltip} onLabelChange={handleLabelChange} />
          <Input placeholder="Enter Default Value" value={placeholder} onChange={handlePlaceholderChange} />
        </div>
        {hint && <Hint text={hint} />}
      </div>

      <div className={`space-y-4 ${validate ? "block" : "hidden"}`}>
        <p className="w-fit whitespace-pre-wrap">{`Confirm: ${label || "Question"}`}</p>
        <div className={`h-10 w-full rounded-lg border border-primary-gray bg-primary-gray/10 px-3 py-2 ${placeholder || "text-primary-gray"}`}>
          {placeholder || "Enter Default Value"}
        </div>
      </div>
    </div>
  );
};

export default ShortAnswerField;
