import { ShortAnswerPropertiesFormData } from "@/app/(core)/forms/types";
import NumberInput from "@/components/NumberInput";
import { FormElement } from "@/components/types";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { findFormElementSectionId } from "@/lib/utils";
import { shortAnswerPropertiesSchema } from "@/schemas/properties/shortAnswerProperties";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

const ShortAnswerProperties = () => {
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const elementId = selectedFormBuilderItem?.id ?? "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) ?? "";
  }

  const form = useForm<ShortAnswerPropertiesFormData>({
    resolver: zodResolver(shortAnswerPropertiesSchema),
    mode: "onChange",
    defaultValues: {
      minimumCharacterCount: selectedFormBuilderItem?.maximumCharacterCount || 0,
      maximumCharacterCount: selectedFormBuilderItem?.maximumCharacterCount || 10,
    },
    shouldFocusError: false,
  });

  const {
    setValue,
    formState: { errors },
  } = form;

  useEffect(() => {
    setValue("minimumCharacterCount", selectedFormBuilderItem?.minimumCharacterCount || 0);
    setValue("maximumCharacterCount", selectedFormBuilderItem?.maximumCharacterCount || 20);
  }, [selectedFormBuilderItem]);

  const applyChanges = (data: ShortAnswerPropertiesFormData) => {
    const newFormElement = {
      ...selectedFormBuilderItem,
      ...data,
    } as FormElement;
    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  return (
    <Form {...form}>
      <form onChange={form.handleSubmit(applyChanges)}>
        <div className="flex flex-col gap-2">
          <FormField
            control={form.control}
            name="minimumCharacterCount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Minimum Number of Characters</FormLabel>
                <FormControl>
                  <NumberInput
                    {...field}
                    placeholder="Minimum Number of  Characters"
                    onChange={field.onChange}
                    className={`${errors.minimumCharacterCount && "border-destructive"}`}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="maximumCharacterCount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Maximum Number of Characters</FormLabel>
                <FormControl>
                  <NumberInput
                    {...field}
                    placeholder="Maximum Number of  Characters"
                    onChange={field.onChange}
                    className={`${errors.maximumCharacterCount && "border-destructive"}`}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  );
};

export default ShortAnswerProperties;
