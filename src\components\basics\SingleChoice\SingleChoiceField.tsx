import FormTooltip from "@/components/FormTooltip";
import Hint from "@/components/Hint";
import LabelInput from "@/components/LabelInput";
import { BaseFieldProps } from "@/components/types";
import { AutosizeTextarea } from "@/components/ui/autosize-textarea";
import { Input } from "@/components/ui/input";
import { useAppDispatch } from "@/hooks/use-redux";
import { updateSelectedFormBuilderItem } from "@/lib/redux/slices/formSlice";
import { updateFormElementOnInputChange } from "@/lib/utils";
import { useEffect } from "react";

function SingleChoiceField({ element, screenId, sectionId }: BaseFieldProps) {
  const dispatch = useAppDispatch();
  useEffect(() => {
    if (element.type === "SingleChoiceField") {
      dispatch(updateSelectedFormBuilderItem(element));
    }
  }, [element, dispatch]);

  const { label, hint, options = [], addOther, tooltip, required, validate } = element;

  const handleLabelChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateFormElementOnInputChange({ label: e.target.value }, screenId, sectionId);
  };

  const handleOptionsChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const newOptions = [...options];
    newOptions && (newOptions[index] = e.target.value);
    updateFormElementOnInputChange({ options: newOptions }, screenId, sectionId);
    updateSelectedFormBuilderItem(element);
  };

  return (
    <div className="relative space-y-6 px-6 pb-8 pt-6">
      <div>
        <div className="space-y-4">
          <LabelInput label={label} required={required} tooltip={tooltip} onLabelChange={handleLabelChange} />
          <div className="rounded-[5px] border border-primary-gray pb-4 pt-4">
            {options.map((option, i) => (
              <div key={i}>
                <div className="flex items-center space-x-2 px-4 pb-[8px] pt-[8px]">
                  <div className="h-[14px] w-[14px] rounded-full border-2 border-solid border-primary-gray" />
                  <Input
                    className="h-6 border-none bg-transparent p-0 text-sm"
                    placeholder={`Option ${i + 1}`}
                    value={option}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleOptionsChange(e, i)}
                  />
                </div>
              </div>
            ))}
            {addOther && (
              <div className="space-y-2 px-4">
                <div className="flex items-center gap-2 pt-[8px]">
                  <div className="h-[14px] w-[14px] rounded-full border-2 border-solid border-primary-gray" />
                  <p className="text-sm text-primary-gray">Add "Other"</p>
                </div>
                <div className="flex h-[3rem] items-center rounded-[10px] border border-primary-gray bg-primary-gray/10 p-2 text-primary-gray">
                  Other
                </div>
              </div>
            )}
          </div>
          {hint && <Hint text={hint} />}
        </div>
      </div>
      <div className={`space-y-4 ${validate ? "block" : "hidden"}`}>
        <div className="flex items-start gap-2">
          <AutosizeTextarea
            className="border-none bg-transparent p-0"
            placeholder="Question"
            value={`Confirm: ${label || "Question"}`}
            readOnly
            onChange={handleLabelChange}
          ></AutosizeTextarea>
          {required || <p className="text-primary-gray">(Optional)</p>}
          {tooltip && <FormTooltip description={tooltip} side="top" />}
        </div>
        <div className="rounded-[5px] border border-primary-gray pb-4 pt-4">
          {options.map((option, i) => (
            <div key={i}>
              <div className="flex items-center space-x-2 px-4 pb-[8px] pt-[8px]">
                <div className="h-[14px] w-[14px] rounded-full border-2 border-solid border-primary-gray" />
                <Input
                  className="h-6 border-none bg-transparent p-0 text-sm"
                  placeholder={`Option ${i + 1}`}
                  value={option}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleOptionsChange(e, i)}
                  readOnly
                />
              </div>
            </div>
          ))}
          {addOther && (
            <div className="space-y-2 px-4">
              <div className="flex items-center gap-2 pt-[8px]">
                <div className="h-[14px] w-[14px] rounded-full border-2 border-solid border-primary-gray" />
                <p className="text-sm text-primary-gray">Add "Other"</p>
              </div>
              <div className="flex h-[3rem] items-center rounded-[10px] border border-primary-gray bg-primary-gray/10 p-2 text-primary-gray">
                Other
              </div>
            </div>
          )}
        </div>
        {hint && <Hint text={hint} />}
      </div>
    </div>
  );
}

export default SingleChoiceField;
