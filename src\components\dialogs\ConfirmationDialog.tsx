import { fredoka } from "@/app/fonts";
import BellIcon from "@/assets/icons/bell.svg";
import { ConfirmationDialogProps } from "@/components/types";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { PRIMARY_COLOUR_GREEN } from "@/lib/constants";
import { closeConfirmationDialog } from "@/lib/redux/slices/dialogSlice";
import Image from "next/image";
import ClipLoader from "react-spinners/ClipLoader";

const ConfirmationDialog = ({ isConfirming, onConfirmation }: ConfirmationDialogProps) => {
  const dispatch = useAppDispatch();
  const isConfirmationDialogOpen = useAppSelector(state => state.dialog.isConfirmationDialogOpen);
  const confirmationMessage = useAppSelector(state => state.dialog.confirmationMessage);

  const handleOpenChange = (open: boolean): void => {
    if (!open) {
      dispatch(closeConfirmationDialog());
    }
  };

  return (
    <AlertDialog open={isConfirmationDialogOpen} onOpenChange={handleOpenChange}>
      <AlertDialogContent className="flex min-h-80 flex-col items-center justify-center px-8">
        <AlertDialogHeader className="mb-5">
          <div className="mb-3 flex items-center justify-center">
            <Image src={BellIcon} alt="Notification Icon" width={48} height={48} />
          </div>
          <AlertDialogTitle className={`${fredoka.className} mb-3 text-center text-[1.5rem] font-semibold`}>Confirm Action</AlertDialogTitle>
          <AlertDialogDescription className="text-center text-[#1E202C]">{confirmationMessage}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex items-center gap-4">
          <AlertDialogCancel className="px-8 py-2 disabled:border-primary-green disabled:text-primary-green" disabled={isConfirming}>
            No
          </AlertDialogCancel>
          <Button variant="outline" className="px-8 py-2 disabled:border-primary-green" disabled={isConfirming} onClick={onConfirmation}>
            {isConfirming ? <ClipLoader color={PRIMARY_COLOUR_GREEN} size={20} /> : "Yes"}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default ConfirmationDialog;
