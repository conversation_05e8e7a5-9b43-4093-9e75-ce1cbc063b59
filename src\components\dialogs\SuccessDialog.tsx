"use client";
import { fredoka } from "@/app/fonts";
import BetterLifeLogo from "@/assets/images/better-life-logo.svg";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { closeSuccessDialog, updateSuccessMessage } from "@/lib/redux/slices/dialogSlice";
import Image from "next/image";
import { Button } from "../ui/button";

const SuccessDialog = () => {
  const dispatch = useAppDispatch();
  const isSuccessDialogOpen = useAppSelector(state => state.dialog.isSuccessDialogOpen);
  const successMessage = useAppSelector(state => state.dialog.successMessage);

  const handleNext = () => {
    dispatch(updateSuccessMessage("Done"));
    dispatch(closeSuccessDialog());
    const returnUrl = sessionStorage.getItem("returnUrl");
    if (returnUrl) {
      window.location.replace(returnUrl);
    }
  };

  return (
    <AlertDialog open={isSuccessDialogOpen}>
      <AlertDialogContent className="flex min-h-80 flex-col items-center justify-center bg-primary-yellow px-8">
        <AlertDialogHeader className="mb-5">
          <AlertDialogTitle className="mb-3 flex items-center justify-center">
            <Image src={BetterLifeLogo} alt="Better Life Image" width={124} height={124} />
          </AlertDialogTitle>
          <AlertDialogDescription className={`text-center text-[#1E202C] ${fredoka.className} text-[1.5rem] font-semibold`}>
            {successMessage || "Done"}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex items-center gap-4">
          <Button className={`h-10 w-[14.25rem] ${fredoka.className} rounded-[10px] text-lg font-semibold`} onClick={handleNext}>
            Next
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default SuccessDialog;
