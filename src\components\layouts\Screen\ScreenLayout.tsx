"use client";
import { fredoka } from "@/app/fonts";
import SectionLayout from "@/components/layouts/Section/SectionLayout";
import type { FormScreen } from "@/components/types";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { updateScreenDetails, updateSelectedFormBuilderItem } from "@/lib/redux/slices/formSlice";
import { cn } from "@/lib/utils";
import { useDroppable } from "@dnd-kit/core";
import { SortableContext } from "@dnd-kit/sortable";
import { useMemo, useState } from "react";
import { VscTriangleUp } from "react-icons/vsc";

const ScreenLayout = ({ screen }: { screen: FormScreen }) => {
  const [isScreenCollapsed, setIsScreenCollapsed] = useState<boolean>(false);
  const dispatch = useAppDispatch();
  const formScreens = useAppSelector(state => state.form.formScreens);
  const sections = screen.sections;
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem);
  const screenIndex = formScreens.findIndex(s => s.id === screen.id);
  const screenNumber = screenIndex + 1;
  const sectionIds = useMemo(() => sections.map(section => section.id), [sections]);

  const handleScreenNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(
      updateScreenDetails({
        screenId: screen.id,
        details: e.target.value,
        detailType: "name",
      }),
    );
  };

  const handleScreenDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    dispatch(
      updateScreenDetails({
        screenId: screen.id,
        details: e.target.value,
        detailType: "description",
      }),
    );
  };

  const handleScreenClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    dispatch(updateSelectedFormBuilderItem({ id: screen.id, type: "ScreenLayout" }));
  };

  const droppable = useDroppable({
    id: screen.id,
    data: {
      isScreen: true,
      screenId: screen.id,
    },
  });

  return (
    <div className="space-y-2">
      <div className="flex h-[2rem] items-center justify-between rounded-[5px] bg-white px-8 py-1 text-center">
        <div>{null}</div>
        <p>{`Screen ${screenNumber}`}</p>
        <VscTriangleUp
          className={`${isScreenCollapsed ? "rotate-180" : "rotate-0"} cursor-pointer text-primary-gray transition duration-200 ease-in-out`}
          onClick={() => setIsScreenCollapsed(!isScreenCollapsed)}
        />
      </div>
      <div
        className={` ${selectedFormBuilderItem?.id === screen.id && !isScreenCollapsed && "border border-primary-green"}`}
        onClick={handleScreenClick}
      >
        {isScreenCollapsed || (
          <div
            ref={droppable.setNodeRef}
            className={cn(`border border-transparent bg-white px-16 py-2`, droppable.isOver && "ring-1 ring-primary-green")}
          >
            <div>
              <p className={`${fredoka.className} text-lg font-semibold`}>Basic</p>
              <div className="mt-4 space-y-4">
                <div className="space-y-1.5">
                  <Label>Screen Name</Label>
                  <Input placeholder="Screen Name" value={screen.name} onChange={handleScreenNameChange} />
                </div>
                <div className="space-y-1.5">
                  <Label className="mb-4">Description (optional)</Label>
                  <Textarea
                    placeholder="Screen Description"
                    className="h-[8rem]"
                    value={screen.description}
                    onChange={handleScreenDescriptionChange}
                  ></Textarea>
                </div>
              </div>
              <p className={`${fredoka.className} my-4 text-lg font-semibold`}>Add Field</p>
              <div className="mt-4">
                <div className="mt-4 pb-36">
                  {droppable.isOver && screen.sections.length === 0 && (
                    <div className="w-full">
                      <div className="h-[120px] rounded-[10px] bg-[#75748F1A]"></div>
                    </div>
                  )}

                  <div className="space-y-3">
                    <SortableContext items={sectionIds}>
                      {screen.sections.map(section => (
                        <SectionLayout section={section} screenId={screen.id} key={section.id} />
                      ))}
                    </SortableContext>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ScreenLayout;
