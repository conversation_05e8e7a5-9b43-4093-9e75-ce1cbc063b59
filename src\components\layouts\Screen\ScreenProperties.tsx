import { fredoka } from "@/app/fonts";
import deleteBinIcon from "@/assets/icons/delete-bin.svg";
import mergeIcon from "@/assets/icons/merge-icon.svg";
import moveDownIcon from "@/assets/icons/move-down.svg";
import moveUpIcon from "@/assets/icons/move-up.svg";
import plusIcon from "@/assets/icons/plus.svg";
import ConfirmationDialog from "@/components/dialogs/ConfirmationDialog";
import { Button } from "@/components/ui/button";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { closeConfirmationDialog, openConfirmationDialog, updateConfirmationMessage } from "@/lib/redux/slices/dialogSlice";
import { replaceFormScreens, updateSelectedFormBuilderItem } from "@/lib/redux/slices/formSlice";
import { createNewScreen, formatScreensForUI, swapArrayElements } from "@/lib/utils";
import Image from "next/image";
import { useState } from "react";
import { VscTriangleRight } from "react-icons/vsc";

const ScreenProperties = () => {
  const [isGeneralOpen, setIsGeneralOpen] = useState<boolean>(true);
  const dispatch = useAppDispatch();
  const formScreens = useAppSelector(state => state.form.formScreens);
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem);
  const screenId = selectedFormBuilderItem?.id as string;
  const currentScreenIndex = formScreens.findIndex(item => item.id === screenId);

  const handleAddScreenBelow = () => {
    const addedScreen = formatScreensForUI([createNewScreen()]);
    const newScreens = [...formScreens];
    newScreens.splice(currentScreenIndex + 1, 0, ...addedScreen);
    dispatch(replaceFormScreens(newScreens));
  };

  const handleDeleteScreenClick = () => {
    if (formScreens.length === 1) return;
    dispatch(
      updateConfirmationMessage(
        "Deleting a screen also deletes the elements in it. To keep the elements, use the 'Merge' button in the properties sidebar.",
      ),
    );
    dispatch(openConfirmationDialog());
  };
  const handleDeleteScreen = () => {
    const newScreens = formScreens.filter(screen => screen.id !== screenId);
    dispatch(replaceFormScreens(newScreens));
    dispatch(updateSelectedFormBuilderItem({ id: formScreens[0]?.id, type: "ScreenLayout" }));
    dispatch(closeConfirmationDialog());
  };

  const handleMoveScreenUp = () => {
    if (currentScreenIndex === 0) return;
    const newScreens = [...formScreens];
    swapArrayElements(newScreens, currentScreenIndex, currentScreenIndex - 1);
    dispatch(replaceFormScreens(newScreens));
  };

  const handleMoveScreenDown = () => {
    if (currentScreenIndex === formScreens.length - 1) return;
    const newScreens = [...formScreens];
    swapArrayElements(newScreens, currentScreenIndex, currentScreenIndex + 1);
    dispatch(replaceFormScreens(newScreens));
  };

  const handleMergeScreens = () => {
    if (currentScreenIndex === 0) return;
    const newScreens = formScreens
      .map((screen, index) => {
        if (index === currentScreenIndex - 1) {
          return {
            ...screen,
            sections: [...screen.sections, ...formScreens[currentScreenIndex].sections],
          };
        }
        return screen;
      })
      .filter(screen => screen.id !== screenId);
    dispatch(replaceFormScreens(newScreens));
  };

  return (
    <div className="h-[201px] w-auto cursor-pointer gap-[0.5rem] pb-2 pt-2">
      <Button
        variant="ghost"
        className={`mb-2 flex items-center gap-4 ${fredoka.className} p-0 text-lg font-semibold hover:bg-transparent`}
        onClick={() => setIsGeneralOpen(!isGeneralOpen)}
      >
        <VscTriangleRight className={`${isGeneralOpen ? "rotate-90" : "rotate-0"} transition duration-200 ease-in-out`} />
        General
      </Button>
      {isGeneralOpen && (
        <div className="ml-4 space-y-2">
          <Button variant="ghost" className="flex cursor-pointer items-center gap-2 p-0 hover:bg-transparent" onClick={handleAddScreenBelow}>
            <Image src={plusIcon} alt="Add Screen" width={14} height={14} />
            Add Screen Below
          </Button>
          <Button
            variant="ghost"
            className={`flex items-center gap-2 ${formScreens.length > 1 ? "cursor-pointer" : "cursor-not-allowed"} p-0 hover:bg-transparent`}
            onClick={handleDeleteScreenClick}
          >
            <Image src={deleteBinIcon} alt="Delete Screen" width={14} height={14} />
            Delete Screen
          </Button>
          <Button
            variant="ghost"
            className={`flex items-center gap-2 p-0 hover:bg-transparent ${currentScreenIndex === 0 ? "cursor-not-allowed" : "cursor-pointer"}`}
            onClick={handleMergeScreens}
          >
            <Image src={mergeIcon} alt="Merge Screens" width={14} height={14} />
            Merge Screens
          </Button>
          <Button
            variant="ghost"
            className={`flex items-center gap-2 p-0 hover:bg-transparent ${currentScreenIndex === 0 ? "cursor-not-allowed" : "cursor-pointer"}`}
            onClick={handleMoveScreenUp}
          >
            <Image src={moveUpIcon} alt="Move Up" width={14} height={14} />
            Move Up
          </Button>
          <Button
            variant="ghost"
            className={`flex items-center gap-2 p-0 hover:bg-transparent ${currentScreenIndex === formScreens.length - 1 ? "cursor-not-allowed" : "cursor-pointer"}`}
            onClick={handleMoveScreenDown}
          >
            <Image src={moveDownIcon} alt="Move Down" width={14} height={14} />
            Move Down
          </Button>
        </div>
      )}
      <ConfirmationDialog isConfirming={false} onConfirmation={handleDeleteScreen} />
    </div>
  );
};

export default ScreenProperties;
