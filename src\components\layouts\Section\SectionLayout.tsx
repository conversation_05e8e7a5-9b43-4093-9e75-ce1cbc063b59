import { fredoka } from "@/app/fonts";
import FormElement from "@/components/FormElement";
import type { ElementType, FormSection } from "@/components/types";
import { Input } from "@/components/ui/input";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { FIELD_COMPONENTS } from "@/lib/constants";
import { updateSectionTitle, updateSelectedFormBuilderItem, updateSelectedFormBuilderItemScreen } from "@/lib/redux/slices/formSlice";
import { cn } from "@/lib/utils";
import { SortableContext, useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useMemo } from "react";

const SectionLayout = ({ section, screenId }: { section: FormSection; screenId: string }) => {
  const { title, id: sectionId } = section;
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem);
  const elements = section.elements;
  const minHeight = Math.max(20, elements.length * 80);
  const elementIds = useMemo(() => elements.map(element => element.id), [elements]);

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(updateSectionTitle({ screenId, sectionId, title: e.target.value }));
  };

  const handleSectionClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    dispatch(updateSelectedFormBuilderItem({ ...section, type: "SectionLayout" }));
    dispatch(updateSelectedFormBuilderItemScreen(screenId));
  };

  const { setNodeRef, attributes, listeners, transform, transition, isDragging, isOver } = useSortable({
    id: section.id,
    data: {
      isSection: true,
      screenId: screenId,
      section,
    },
  });

  const style = {
    transition,
    transform: CSS.Transform.toString(transform),
    minHeight: `${minHeight}px`,
  };

  return (
    <>
      {isDragging ? (
        <div
          ref={setNodeRef}
          style={style}
          className="relative -mx-4 h-[9rem] cursor-pointer rounded-[10px] border-2 border-primary-green p-4 opacity-60"
        ></div>
      ) : (
        <div
          ref={setNodeRef}
          {...attributes}
          {...listeners}
          style={style}
          className={cn(
            `relative -mx-4 cursor-pointer rounded-[10px] border border-primary-gray p-4`,
            selectedFormBuilderItem?.id === section.id && "border-primary-green",
            isOver && "border-primary-green",
          )}
          onClick={handleSectionClick}
        >
          <div className="relative space-y-8">
            <Input
              className={`${fredoka.className} h-6 border-none bg-transparent p-0 text-lg font-semibold`}
              placeholder="Section Name"
              value={title}
              onChange={handleTitleChange}
            />
          </div>
          <div className={`mt-4 min-h-[${minHeight}px] pb-24`}>
            <div className="space-y-4">
              <SortableContext items={elementIds}>
                {section.elements.map(element => {
                  const Element = FIELD_COMPONENTS[element.type as ElementType];
                  return <FormElement key={element.id} element={element} Element={Element} screenId={screenId} sectionId={sectionId} />;
                })}
              </SortableContext>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SectionLayout;
