import { createForm, submitForm, updateForm, uploadFile } from "@/api/forms";
import { CreateFormServerData, UpdateFormServerData } from "@/app/(core)/forms/types";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { useToast } from "@/hooks/use-toast";
import { closeCreateFormDialog, openSuccessDialog, updateSuccessMessage } from "@/lib/redux/slices/dialogSlice";
import { invalidateQueries, updateFormElementOnInputChange } from "@/lib/utils";
import { useMutation } from "@tanstack/react-query";
import { AxiosError, AxiosProgressEvent } from "axios";
import { useRouter } from "next/navigation";

export const useUploadReadonlyFile = (screenId: string, sectionId: string, setUploadProgress: (progress: number) => void) => {
  const { toast } = useToast();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const onUploadProgress = (progressEvent: AxiosProgressEvent) => {
    const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
    setUploadProgress(percentCompleted);
  };

  const { mutate: uploadReadonlyFile, isPending: isUploadingReadonlyFile } = useMutation({
    mutationFn: (formData: File) => uploadFile(formData, onUploadProgress),
    onSuccess: (fileUrl: string) => {
      updateFormElementOnInputChange({ fileUrl }, screenId, sectionId);
    },
    onError: (error: unknown) => {
      if (error instanceof AxiosError) {
        toast({
          description: error?.response?.data.message,
        });
        return;
      }
      toast({
        description: "Failed to upload file",
      });
    },
  });
  return { uploadReadonlyFile, isUploadingReadonlyFile };
};

export const useCreateForm = () => {
  const { toast } = useToast();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { mutate: createNewForm, isPending: isCreatingNewForm } = useMutation({
    mutationFn: (formData: CreateFormServerData) => createForm(formData),
    onSuccess: data => {
      dispatch(closeCreateFormDialog());
      router.push(`/forms/${data.id}`);
    },
    onError: (error: unknown) => {
      dispatch(closeCreateFormDialog());
      if (error instanceof AxiosError) {
        toast({
          description: error?.response?.data.message,
        });
        return;
      }
      toast({
        description: "Failed to create form",
      });
    },
  });
  return { isCreatingNewForm, createNewForm };
};

export const useUpdateForm = () => {
  const { toast } = useToast();
  const dispatch = useAppDispatch();
  const formId = useAppSelector(state => state.form.formId);
  const {
    mutate: updateSingleForm,
    isPending: isUpdatingSingleForm,
    isSuccess: isUpdateSingleFormSuccess,
    data: updateSingleFormData,
  } = useMutation({
    mutationFn: (formData: UpdateFormServerData) => updateForm(formId, formData),
    onSuccess: () => {
      invalidateQueries("single-form");
      dispatch(closeCreateFormDialog());
    },
    onError: (error: unknown) => {
      if (error instanceof AxiosError) {
        toast({
          description: error?.response?.data.message,
        });
        return;
      }
      toast({
        description: "Failed to update screens",
      });
    },
  });

  return { updateSingleForm, isUpdatingSingleForm, isUpdateSingleFormSuccess, updateSingleFormData: updateSingleFormData as any };
};

export const useSubmitForm = () => {
  const { toast } = useToast();
  const dispatch = useAppDispatch();
  const formId = useAppSelector(state => state.form.formId);
  const { mutate: submitSingleForm, isPending: isSubmittingSingleForm } = useMutation({
    mutationFn: () => submitForm(formId),
    onSuccess: () => {
      dispatch(updateSuccessMessage("Form has been submitted successfully"));
      dispatch(openSuccessDialog());
      invalidateQueries(["single-form", "existing-forms"]);
    },
    onError: (error: unknown) => {
      if (error instanceof AxiosError) {
        toast({
          description: error?.response?.data.message,
        });
        return;
      }
      toast({
        description: "Failed to submit form",
      });
    },
  });

  return { submitSingleForm, isSubmittingSingleForm };
};
