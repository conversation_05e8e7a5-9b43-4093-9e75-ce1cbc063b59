import { getExistingForms, getSingleForm, uploadFile } from "@/api/forms";
import { ExistingFormData, ExistingFormsQueryParams, SingleFormData } from "@/app/(core)/forms/types";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { AxiosError, AxiosProgressEvent } from "axios";
import { useState } from "react";

export const useGetSingleForm = (id: string) => {
  const {
    isLoading: singleFormLoading,
    data: singleFormData,
    error: singleFormError,
  } = useQuery<SingleFormData, AxiosError>({
    queryKey: ["single-form", id],
    queryFn: () => getSingleForm(id),
  });
  return { singleFormLoading, singleFormData, singleFormError };
};

export const useGetExistingForms = (params: ExistingFormsQueryParams) => {
  const {
    isLoading: existingFormsLoading,
    data: existingForms,
    error: existingFormsError,
  } = useQuery<ExistingFormData, AxiosError>({
    queryKey: ["existing-forms", Object.values(params)],
    queryFn: () => getExistingForms(params),
    placeholderData: keepPreviousData,
  });
  return { existingFormsLoading, existingForms, existingFormsError };
};
