import { FormScreen } from "@/components/types";
import { useUpdateForm } from "@/hooks/tansack-query/mutations/use-forms";
import useDebounce from "@/hooks/use-debounce";
import { useAppSelector } from "@/hooks/use-redux";
import { formatScreensForApi } from "@/lib/utils";
import { useEffect } from "react";

const useAutosave = () => {
  const formScreens = useAppSelector(state => state.form.formScreens);
  const debouncedFormScreens = useDebounce<FormScreen[]>(formScreens);
  const { updateSingleForm, isUpdatingSingleForm: isSaving, isUpdateSingleFormSuccess: isSaved } = useUpdateForm();

  useEffect(() => {
    const formattedScreens = formatScreensForApi(debouncedFormScreens);
    if (!formattedScreens.length) return;
    updateSingleForm({ screens: formattedScreens });
  }, [debouncedFormScreens]);

  return { isSaving, isSaved };
};

export default useAutosave;
