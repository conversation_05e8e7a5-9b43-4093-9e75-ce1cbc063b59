import type { AuthState } from "@/lib/redux/types";
import { createSlice } from "@reduxjs/toolkit";

const initialState: AuthState = {
  userTokens: {
    accessToken: "",
    refreshToken: "",
  },
  isSomethingWrong: false,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    updateAccessToken: (state, { payload }: { payload: string }) => {
      state.userTokens.accessToken = payload;
    },

    updateRefreshToken: (state, { payload }: { payload: string }) => {
      state.userTokens.refreshToken = payload;
    },

    updateUserTokens: (state, { payload }: { payload: { accessToken: string; refreshToken: string } }) => {
      state.userTokens = payload;
    },
    updateSomethingWrong: (state, { payload }: { payload: boolean }) => {
      state.isSomethingWrong = payload;
    },
  },
});

export const { updateAccessToken, updateRefreshToken, updateUserTokens, updateSomethingWrong } = authSlice.actions;

export default authSlice.reducer;
