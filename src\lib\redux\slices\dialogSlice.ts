import type { DialogState } from "@/lib/redux/types";
import { createSlice } from "@reduxjs/toolkit";

const initialState: DialogState = {
  isConfirmationDialogOpen: false,
  isCreateFormDialogOpen: false,
  isSuccessDialogOpen: false,
  successMessage: "",
  confirmationMessage: "",
};

const dialogSlice = createSlice({
  name: "dialog",
  initialState,
  reducers: {
    openConfirmationDialog: state => {
      state.isConfirmationDialogOpen = true;
    },
    closeConfirmationDialog: state => {
      state.isConfirmationDialogOpen = false;
    },
    openCreateFormDialog: state => {
      state.isCreateFormDialogOpen = true;
    },
    closeCreateFormDialog: state => {
      state.isCreateFormDialogOpen = false;
    },
    updateSuccessMessage: (state, { payload }: { payload: string }) => {
      state.successMessage = payload;
    },
    updateConfirmationMessage: (state, { payload }: { payload: string }) => {
      state.confirmationMessage = payload;
    },
    openSuccessDialog: state => {
      state.isSuccessDialogOpen = true;
    },
    closeSuccessDialog: state => {
      state.isSuccessDialogOpen = false;
    },
  },
});

export const {
  openConfirmationDialog,
  closeConfirmationDialog,
  openCreateFormDialog,
  closeCreateFormDialog,
  openSuccessDialog,
  closeSuccessDialog,
  updateSuccessMessage,
  updateConfirmationMessage,
} = dialogSlice.actions;

export default dialogSlice.reducer;
