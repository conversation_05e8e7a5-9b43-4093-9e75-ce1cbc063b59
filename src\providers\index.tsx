"use client";
import queryClient from "@/config/tansack-query";
import AuthWrapper from "@/components/AuthWrapper";
import useDndSensors from "@/hooks/use-dnd-sensors";
import { persistor, store } from "@/lib/redux/store";
import { DndContext } from "@dnd-kit/core";
import { QueryClientProvider } from "@tanstack/react-query";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import { ReactNode } from "react";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";

const Providers = ({ children }: { children: ReactNode }) => {
  const sensors = useDndSensors();
  return (
    <QueryClientProvider client={queryClient}>
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          <DndContext sensors={sensors}>
            <NuqsAdapter>{children}</NuqsAdapter>
          </DndContext>
        </PersistGate>
      </Provider>
    </QueryClientProvider>
  );
};

export default Providers;
