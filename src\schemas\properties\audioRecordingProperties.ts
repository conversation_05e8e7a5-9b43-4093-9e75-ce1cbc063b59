import { MAX_AUDIO_RECORDING_DURATION, MIN_AUDIO_RECORDING_DURATION, TIME_REGEX } from "@/lib/constants";
import { z } from "zod";

// Helper function to convert hh:mm:ss to seconds
function timeToSeconds(timeString: string): number {
  const [hours, minutes, seconds] = timeString.split(":").map(Number);
  return hours * 3600 + minutes * 60 + seconds;
}

export const audioRecordingPropertiesSchema = z
  .object({
    minimumDuration: z
      .string()
      .regex(TIME_REGEX, {
        message: "Invalid time format (hh:mm:ss)",
      })
      .optional(),
    maximumDuration: z
      .string()
      .regex(TIME_REGEX, {
        message: "Invalid time format (hh:mm:ss)",
      })
      .optional(),
  })
  .superRefine(({ minimumDuration, maximumDuration }, ctx) => {
    if (!minimumDuration || !maximumDuration) return;

    try {
      const minSeconds = timeToSeconds(minimumDuration);
      const maxSeconds = timeToSeconds(maximumDuration);
      const maxAllowedSeconds = timeToSeconds(MAX_AUDIO_RECORDING_DURATION);
      const minAllowedSeconds = timeToSeconds(MIN_AUDIO_RECORDING_DURATION);

      if (minSeconds < minAllowedSeconds) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Minimum duration can't be less than ${MIN_AUDIO_RECORDING_DURATION}`,
          path: ["minimumDuration"],
        });
      }

      if (minSeconds >= maxSeconds) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Minimum duration must be less than maximum duration",
          path: ["minimumDuration"],
        });
      }

      if (maxSeconds > maxAllowedSeconds) {
        // Use > instead of >=
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Maximum duration can't exceed ${MAX_AUDIO_RECORDING_DURATION}`,
          path: ["maximumDuration"],
        });
      }
    } catch (error) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Invalid time format",
        path: ["minimumDuration"], // Or ["maximumDuration"] or both, depending on the error's origin.
      });
    }
  });
