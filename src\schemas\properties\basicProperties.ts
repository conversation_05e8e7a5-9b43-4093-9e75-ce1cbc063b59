import { isTagDuplicate } from "@/lib/utils";
import { z } from "zod";

export const getBasicPropertiesSchema = (currentElementId: string) =>
  z
    .object({
      hint: z.string().optional(),
      tooltip: z.string().optional(),
      required: z.boolean().optional(),
      validate: z.boolean().optional(),
      tag: z.string().trim().min(1, { message: "Tag is required" }).max(25, { message: "Tag cannot exceed 25 characters" }).optional(),
    })
    .superRefine(({ tag }, ctx) => {
      if (tag && isTagDuplicate(tag, currentElementId)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Enter a unique tag",
          path: ["tag"],
        });
      }
    });
