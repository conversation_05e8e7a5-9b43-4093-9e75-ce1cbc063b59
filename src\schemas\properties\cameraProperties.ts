import { MAX_VIDEO_DURATION, TIME_REGEX } from "@/lib/constants";
import { z } from "zod";

export const cameraPropertiesSchema = z
  .object({
    minimumDuration: z
      .string()
      .regex(TIME_REGEX, {
        message: "Invalid time format (hh:mm:ss)",
      })
      .optional(),
    maximumDuration: z
      .string()
      .regex(TIME_REGEX, {
        message: "Invalid time format (hh:mm:ss)",
      })
      .optional(),
    mediaType: z.string().min(1, { message: "Media type is required" }),
  })
  .superRefine(({ minimumDuration, maximumDuration }, ctx) => {
    if (!minimumDuration || !maximumDuration) return;
    const formattedMaxVideoDuration = MAX_VIDEO_DURATION.replaceAll(":", "");
    const formattedMinDuration = minimumDuration.replaceAll(":", "");
    const formattedMaxDuration = maximumDuration.replaceAll(":", "");

    if (+formattedMinDuration >= +formattedMaxDuration) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Minimum duration must be less than maximum duration",
        path: ["minimumDuration"],
      });
    }

    if (+formattedMaxDuration >= +formattedMaxVideoDuration) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Maximum duration can't exceed ${MAX_VIDEO_DURATION}`,
        path: ["maximumDuration"],
      });
    }
  });
