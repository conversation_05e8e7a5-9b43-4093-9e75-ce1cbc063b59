import { z } from "zod";

export const dropdownPropertiesSchema = z
  .object({
    isCorrectAnswerTicked: z.boolean().optional(),
    correctAnswer: z.string().optional(),
  })
  .superRefine(({ isCorrectAnswerTicked, correctAnswer }, ctx) => {
    if (isCorrectAnswerTicked && !correctAnswer) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Select a correct answer",
        path: ["correctAnswer"],
      });
    }
  });
