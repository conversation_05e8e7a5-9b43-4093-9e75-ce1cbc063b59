import { FILE_SIZE } from "@/lib/constants";
import { z } from "zod";

export const filePropertiesSchema = z
  .object({
    minimumFileSize: z
      .number({ message: "Enter minimum file size" })
      .min(FILE_SIZE.MINIMUM, { message: `Minimum file size is ${FILE_SIZE.MINIMUM}MB` }),
    maximumFileSize: z
      .number({ message: "Enter maximum file size" })
      .max(FILE_SIZE.MAXIMUM, { message: `Maximum file size is ${FILE_SIZE.MAXIMUM}MB` }),
    fileType: z.string().min(1, { message: "File type is required" }),
    mp4: z.boolean().optional(),
    mpeg: z.boolean().optional(),
    avi: z.boolean().optional(),
    pdf: z.boolean().optional(),
    doc: z.boolean().optional(),
    docx: z.boolean().optional(),
    xls: z.boolean().optional(),
    xlsx: z.boolean().optional(),
    csv: z.boolean().optional(),
    mp3: z.boolean().optional(),
    wav: z.boolean().optional(),
    aac: z.boolean().optional(),
    jpg: z.boolean().optional(),
    png: z.boolean().optional(),
    allowMultipleFiles: z.boolean(),
  })
  .superRefine(({ minimumFileSize, maximumFileSize }, ctx) => {
    if (minimumFileSize >= maximumFileSize) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Minimum file size must be less than maximum file size",
        path: ["minimumFileSize"],
      });
    }
  });
