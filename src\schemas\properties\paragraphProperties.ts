import { PARAGRAPH_CHARACTER_COUNT } from "@/lib/constants";
import { z } from "zod";

export const paragraphPropertiesSchema = z
  .object({
    minimumCharacterCount: z
      .number({ message: "Enter minimum number of characters" })
      .min(PARAGRAPH_CHARACTER_COUNT.MINIMUM, { message: `Minimum character limit is ${PARAGRAPH_CHARACTER_COUNT.MINIMUM}` }),
    maximumCharacterCount: z
      .number({ message: "Enter maximum number of characters" })
      .max(PARAGRAPH_CHARACTER_COUNT.MAXIMUM, { message: `Maximum character limit is ${PARAGRAPH_CHARACTER_COUNT.MAXIMUM}` }),
  })
  .superRefine(({ minimumCharacterCount, maximumCharacterCount }, ctx) => {
    if (minimumCharacterCount >= maximumCharacterCount) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Minimum character limit must be less than maximum character limit",
        path: ["minimumCharacterCount"],
      });
    }
  });
