import { SHORT_ANSWER_CHARACTER_COUNT } from "@/lib/constants";
import { z } from "zod";

export const shortAnswerPropertiesSchema = z
  .object({
    minimumCharacterCount: z
      .number({ message: "Enter minimum number of characters" })
      .min(SHORT_ANSWER_CHARACTER_COUNT.MINIMUM, { message: `Minimum character limit is ${SHORT_ANSWER_CHARACTER_COUNT.MINIMUM}` }),
    maximumCharacterCount: z
      .number({ message: "Enter maximum number of characters" })
      .max(SHORT_ANSWER_CHARACTER_COUNT.MAXIMUM, { message: `Maximum character limit is ${SHORT_ANSWER_CHARACTER_COUNT.MAXIMUM}` }),
  })
  .superRefine(({ minimumCharacterCount, maximumCharacterCount }, ctx) => {
    if (minimumCharacterCount >= maximumCharacterCount) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Minimum character limit must be less than maximum character limit",
        path: ["minimumCharacterCount"],
      });
    }
  });
